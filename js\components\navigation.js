/**
 * 导航组件 - 处理导航相关的交互逻辑
 */

class NavigationComponent {
    constructor() {
        this.activeNavItem = null;
        this.navigationHistory = [];
        this.maxHistoryLength = 10;
        
        this.init();
    }
    
    init() {
        this.initNavigationTooltips();
        this.initNavigationAnimations();
        this.initBreadcrumb();
    }
    
    // 初始化导航工具提示
    initNavigationTooltips() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            const navText = link.querySelector('.nav-text');
            if (navText) {
                link.setAttribute('title', navText.textContent.trim());
            }
            
            // 为折叠状态添加悬停效果
            link.addEventListener('mouseenter', (e) => {
                this.showNavigationTooltip(e.currentTarget);
            });
            
            link.addEventListener('mouseleave', (e) => {
                this.hideNavigationTooltip(e.currentTarget);
            });
        });
    }
    
    // 显示导航工具提示
    showNavigationTooltip(navLink) {
        const sidebar = document.querySelector('.app-sidebar');
        if (!sidebar || !sidebar.classList.contains('collapsed')) {
            return;
        }
        
        const tooltip = this.createTooltip(navLink);
        if (tooltip) {
            document.body.appendChild(tooltip);
            this.positionTooltip(tooltip, navLink);
        }
    }
    
    // 隐藏导航工具提示
    hideNavigationTooltip(navLink) {
        const existingTooltip = document.querySelector('.nav-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }
    }
    
    // 创建工具提示元素
    createTooltip(navLink) {
        const navText = navLink.querySelector('.nav-text');
        if (!navText) return null;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'nav-tooltip';
        tooltip.textContent = navText.textContent.trim();
        
        // 添加样式
        Object.assign(tooltip.style, {
            position: 'fixed',
            backgroundColor: 'var(--neutral-primary)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            zIndex: '1000',
            opacity: '0',
            transform: 'translateY(-50%)',
            transition: 'opacity 0.2s ease-in-out',
            pointerEvents: 'none',
            boxShadow: 'var(--shadow-depth-8)'
        });
        
        // 淡入动画
        requestAnimationFrame(() => {
            tooltip.style.opacity = '1';
        });
        
        return tooltip;
    }
    
    // 定位工具提示
    positionTooltip(tooltip, navLink) {
        const linkRect = navLink.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        const left = linkRect.right + 12;
        const top = linkRect.top + (linkRect.height / 2);
        
        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
        
        // 确保工具提示不会超出屏幕边界
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        if (left + tooltipRect.width > viewportWidth) {
            tooltip.style.left = `${linkRect.left - tooltipRect.width - 12}px`;
        }
        
        if (top + tooltipRect.height / 2 > viewportHeight) {
            tooltip.style.top = `${viewportHeight - tooltipRect.height - 12}px`;
        }
        
        if (top - tooltipRect.height / 2 < 0) {
            tooltip.style.top = '12px';
        }
    }
    
    // 初始化导航动画
    initNavigationAnimations() {
        const navItems = document.querySelectorAll('.nav-item');
        
        // 为导航项添加进入动画
        navItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.05}s`;
        });
        
        // 添加点击波纹效果
        navItems.forEach(item => {
            const navLink = item.querySelector('.nav-link');
            if (navLink) {
                navLink.addEventListener('click', (e) => {
                    this.createRippleEffect(e);
                });
            }
        });
    }
    
    // 创建波纹效果
    createRippleEffect(event) {
        const button = event.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.className = 'nav-ripple';
        
        Object.assign(ripple.style, {
            position: 'absolute',
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}px`,
            top: `${y}px`,
            borderRadius: '50%',
            backgroundColor: 'rgba(0, 95, 184, 0.3)',
            transform: 'scale(0)',
            animation: 'ripple 0.6s ease-out',
            pointerEvents: 'none'
        });
        
        // 确保按钮有相对定位
        if (getComputedStyle(button).position === 'static') {
            button.style.position = 'relative';
        }
        
        button.appendChild(ripple);
        
        // 动画结束后移除元素
        ripple.addEventListener('animationend', () => {
            ripple.remove();
        });
    }
    
    // 初始化面包屑导航
    initBreadcrumb() {
        this.updateBreadcrumb();
    }
    
    // 更新面包屑导航
    updateBreadcrumb(currentPage = 'connection') {
        const breadcrumbContainer = document.querySelector('.breadcrumb');
        if (!breadcrumbContainer) return;
        
        const pageHierarchy = this.getPageHierarchy(currentPage);
        const breadcrumbHTML = pageHierarchy.map((page, index) => {
            const isLast = index === pageHierarchy.length - 1;
            const className = isLast ? 'breadcrumb-item active' : 'breadcrumb-item';
            
            if (isLast) {
                return `<span class="${className}">${page.title}</span>`;
            } else {
                return `<a href="#" class="${className}" data-page="${page.id}">${page.title}</a>`;
            }
        }).join('<span class="breadcrumb-separator">/</span>');
        
        breadcrumbContainer.innerHTML = breadcrumbHTML;
        
        // 添加面包屑点击事件
        breadcrumbContainer.querySelectorAll('a[data-page]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageId = e.currentTarget.dataset.page;
                if (window.app) {
                    window.app.navigateToPage(pageId);
                }
            });
        });
    }
    
    // 获取页面层级结构
    getPageHierarchy(currentPage) {
        const pages = {
            'connection': { id: 'connection', title: '连接管理', parent: null },
            'monitor': { id: 'monitor', title: '实时监控', parent: null },
            'control': { id: 'control', title: '设备控制', parent: null },
            'analysis': { id: 'analysis', title: '数据分析', parent: null },
            'debug': { id: 'debug', title: '调试工具', parent: null },
            'system': { id: 'system', title: '系统管理', parent: null }
        };
        
        const hierarchy = [];
        let current = pages[currentPage];
        
        while (current) {
            hierarchy.unshift(current);
            current = current.parent ? pages[current.parent] : null;
        }
        
        // 添加首页
        hierarchy.unshift({ id: 'home', title: 'AirMonitor Pro', parent: null });
        
        return hierarchy;
    }
    
    // 添加导航历史记录
    addToHistory(pageId) {
        // 避免重复添加相同页面
        if (this.navigationHistory.length > 0 && 
            this.navigationHistory[this.navigationHistory.length - 1] === pageId) {
            return;
        }
        
        this.navigationHistory.push(pageId);
        
        // 限制历史记录长度
        if (this.navigationHistory.length > this.maxHistoryLength) {
            this.navigationHistory.shift();
        }
        
        this.updateNavigationButtons();
    }
    
    // 更新导航按钮状态
    updateNavigationButtons() {
        const backButton = document.querySelector('.nav-back-btn');
        const forwardButton = document.querySelector('.nav-forward-btn');
        
        if (backButton) {
            backButton.disabled = this.navigationHistory.length <= 1;
        }
        
        if (forwardButton) {
            // 前进按钮逻辑需要更复杂的历史记录管理
            forwardButton.disabled = true;
        }
    }
    
    // 后退导航
    goBack() {
        if (this.navigationHistory.length > 1) {
            this.navigationHistory.pop(); // 移除当前页面
            const previousPage = this.navigationHistory[this.navigationHistory.length - 1];
            
            if (window.app && previousPage) {
                window.app.navigateToPage(previousPage);
            }
        }
    }
    
    // 高亮当前导航项
    highlightCurrentNavItem(pageId) {
        // 移除所有激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // 激活当前页面对应的导航项
        const currentNavItem = document.querySelector(`[data-page="${pageId}"]`);
        if (currentNavItem) {
            currentNavItem.classList.add('active');
            this.activeNavItem = currentNavItem;
        }
        
        // 更新面包屑
        this.updateBreadcrumb(pageId);
        
        // 添加到历史记录
        this.addToHistory(pageId);
    }
    
    // 获取当前激活的导航项
    getCurrentNavItem() {
        return this.activeNavItem;
    }
    
    // 获取导航历史
    getNavigationHistory() {
        return [...this.navigationHistory];
    }
}

// 添加波纹动画的CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    .nav-tooltip {
        font-family: var(--font-family);
    }
`;
document.head.appendChild(rippleStyle);

// 导出导航组件
window.NavigationComponent = NavigationComponent;

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.navigationComponent = new NavigationComponent();
});
