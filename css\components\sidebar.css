/* ===== 左侧导航栏样式 ===== */

.app-sidebar {
  display: flex;
  flex-direction: column;
  width: var(--sidebar-width);
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  transition: width 0.3s ease-in-out;
  flex-shrink: 0;
  z-index: 100;
}

.app-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 导航菜单 */
.nav-menu {
  list-style: none;
  padding: var(--spacing-xs) 0;
  margin: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--neutral-primary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: 400;
  transition: all 0.15s ease-in-out;
  border-left: 2px solid transparent;
  position: relative;
  height: 32px;
}

.nav-link:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.nav-link:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: -2px;
}

/* 激活状态 */
.nav-item.active .nav-link {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  font-weight: 500;
}

.nav-item.active .nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--primary-color);
}

/* 导航图标 */
.nav-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  transition: color 0.15s ease-in-out;
}

.nav-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity 0.3s ease-in-out;
}

/* 折叠状态下的样式 */
.app-sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.app-sidebar.collapsed .nav-link {
  padding: var(--spacing-sm);
  justify-content: center;
  gap: 0;
}

.app-sidebar.collapsed .nav-link:hover {
  position: relative;
}

/* 折叠状态下的工具提示 */
.app-sidebar.collapsed .nav-link::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--neutral-primary);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  z-index: 1000;
  margin-left: var(--spacing-sm);
}

.app-sidebar.collapsed .nav-link:hover::after {
  opacity: 1;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: var(--spacing-sm);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 28px;
  border: none;
  border-radius: var(--border-radius-sm);
  background-color: transparent;
  color: var(--neutral-secondary);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.sidebar-toggle:hover {
  background-color: var(--bg-tertiary);
  color: var(--neutral-primary);
}

.sidebar-toggle:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.sidebar-toggle svg {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease-in-out;
}

.app-sidebar.collapsed .sidebar-toggle svg {
  transform: rotate(180deg);
}

/* 导航项悬停效果 */
.nav-item {
  position: relative;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background-color: var(--primary-color);
  transition: width 0.2s ease-in-out;
}

.nav-item:hover::before {
  width: 2px;
}

.nav-item.active::before {
  width: 2px;
}

/* 导航分组（如果需要） */
.nav-group {
  margin: var(--spacing-md) 0;
}

.nav-group-title {
  padding: var(--spacing-xs) var(--spacing-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--neutral-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.app-sidebar.collapsed .nav-group-title {
  display: none;
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .app-sidebar {
    width: 200px;
  }
  
  .nav-link {
    padding: var(--spacing-sm) var(--spacing-md);
    gap: var(--spacing-sm);
  }
  
  .nav-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 1280px) {
  .app-sidebar {
    width: 180px;
  }
  
  .nav-link {
    font-size: var(--font-size-sm);
  }
  
  .sidebar-footer {
    padding: var(--spacing-sm);
  }
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--neutral-quaternary);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-tertiary);
}

/* 动画效果 */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.nav-item {
  animation: slideIn 0.3s ease-out;
}

.nav-item:nth-child(1) { animation-delay: 0.05s; }
.nav-item:nth-child(2) { animation-delay: 0.1s; }
.nav-item:nth-child(3) { animation-delay: 0.15s; }
.nav-item:nth-child(4) { animation-delay: 0.2s; }
.nav-item:nth-child(5) { animation-delay: 0.25s; }
.nav-item:nth-child(6) { animation-delay: 0.3s; }
