/* ===== 主内容区域样式 ===== */

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--bg-secondary);
}

.main-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  background-color: var(--bg-secondary);
}

/* 内容容器 */
.content-container {
  max-width: 100%;
  margin: 0 auto;
  padding: var(--spacing-md);
  min-height: 100%;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-depth-4);
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-title h1 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--neutral-primary);
  margin: 0;
}

.page-title .page-icon {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
}

.page-subtitle {
  font-size: var(--font-size-sm);
  color: var(--neutral-secondary);
  margin-top: var(--spacing-xs);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 内容卡片 */
.content-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-depth-4);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  transition: box-shadow 0.2s ease-in-out;
}

.content-card:hover {
  box-shadow: var(--shadow-depth-8);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--neutral-primary);
  margin: 0;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.card-body {
  padding: var(--spacing-md);
}

.card-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* 网格布局 */
.content-grid {
  display: grid;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.content-grid.cols-1 {
  grid-template-columns: 1fr;
}

.content-grid.cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.content-grid.cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.content-grid.cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 欢迎屏幕特殊样式 */
.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  position: relative;
}

.welcome-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 95, 184, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 120, 212, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.welcome-content {
  text-align: center;
  max-width: 600px;
  padding: var(--spacing-xl);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-depth-16);
  position: relative;
  z-index: 1;
}

.welcome-content h1 {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--neutral-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-content p {
  font-size: var(--font-size-sm);
  color: var(--neutral-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.4;
}

/* 状态概览 */
.status-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: var(--primary-light);
  border-radius: var(--border-radius-sm);
  color: var(--primary-color);
  flex-shrink: 0;
}

.status-info {
  flex: 1;
  min-width: 0;
}

.status-label {
  font-size: var(--font-size-xs);
  color: var(--neutral-tertiary);
  font-weight: 500;
  margin-bottom: 1px;
  line-height: 1;
}

.status-value {
  font-size: var(--font-size-sm);
  color: var(--neutral-primary);
  font-weight: 600;
  line-height: 1;
}

.quick-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease-in-out;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn svg {
  flex-shrink: 0;
  transition: transform 0.2s ease-in-out;
}

.action-btn:hover svg {
  transform: scale(1.1);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  flex-direction: column;
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--neutral-secondary);
  font-size: var(--font-size-base);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: var(--neutral-secondary);
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
}

.empty-state-description {
  font-size: var(--font-size-base);
  line-height: 1.5;
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .content-container {
    padding: var(--spacing-md);
  }
  
  .page-header {
    padding: var(--spacing-md);
  }
  
  .content-grid.cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .content-grid.cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1280px) {
  .content-container {
    padding: var(--spacing-sm);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .page-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .content-grid.cols-3,
  .content-grid.cols-4 {
    grid-template-columns: 1fr;
  }
  
  .content-grid.cols-2 {
    grid-template-columns: 1fr;
  }
  
  .welcome-content {
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
  }
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.main-content::-webkit-scrollbar-thumb {
  background: var(--neutral-quaternary);
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-tertiary);
}
