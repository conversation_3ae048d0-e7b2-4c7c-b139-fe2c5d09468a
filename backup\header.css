/* ===== 顶部标题栏样式 ===== */

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  padding: 0 var(--spacing-md);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-depth-4);
  z-index: 1000;
}

/* 左侧区域 - Logo和标题 */
.header-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
  flex-shrink: 0;
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--neutral-primary);
  margin-right: var(--spacing-xs);
}

.app-subtitle {
  font-size: var(--font-size-sm);
  color: var(--neutral-secondary);
  font-weight: 400;
}

/* 中间区域 - 连接状态 */
.header-center {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
  max-width: 400px;
  margin: 0 var(--spacing-lg);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  min-width: 200px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  animation: pulse 2s infinite;
}

.status-dot.connected {
  background-color: var(--success-color);
}

.status-dot.disconnected {
  background-color: var(--error-color);
}

.status-dot.connecting {
  background-color: var(--warning-color);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--neutral-primary);
}

.device-info {
  flex: 1;
  text-align: right;
}

.device-name {
  font-size: var(--font-size-sm);
  color: var(--neutral-secondary);
  font-style: italic;
}

/* 右侧区域 - 授权信息和操作按钮 */
.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
}

.license-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.license-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.license-icon {
  width: 16px;
  height: 16px;
  color: var(--success-color);
}

.license-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--success-color);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: var(--font-size-xs);
  color: var(--neutral-secondary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius-md);
  background-color: transparent;
  color: var(--neutral-secondary);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.header-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--neutral-primary);
}

.header-btn:active {
  background-color: var(--border-color);
}

.header-btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.header-btn svg {
  width: 16px;
  height: 16px;
}

/* 状态指示器动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 连接状态变化动画 */
.status-dot {
  transition: background-color 0.3s ease-in-out;
}

.connection-status {
  transition: border-color 0.3s ease-in-out;
}

.connection-status.connected {
  border-color: var(--success-color);
  background-color: rgba(16, 124, 16, 0.05);
}

.connection-status.disconnected {
  border-color: var(--error-color);
  background-color: rgba(209, 52, 56, 0.05);
}

.connection-status.connecting {
  border-color: var(--warning-color);
  background-color: rgba(255, 140, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .header-center {
    max-width: 300px;
    margin: 0 var(--spacing-md);
  }
  
  .connection-status {
    min-width: 180px;
  }
  
  .app-subtitle {
    display: none;
  }
}

@media (max-width: 1280px) {
  .app-header {
    padding: 0 var(--spacing-sm);
  }
  
  .header-center {
    max-width: 250px;
    margin: 0 var(--spacing-sm);
  }
  
  .connection-status {
    min-width: 160px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .license-info {
    display: none;
  }
  
  .device-info {
    display: none;
  }
}
