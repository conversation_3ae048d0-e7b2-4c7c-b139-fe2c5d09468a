/**
 * 商用空调调试监控软件 - 主JavaScript文件
 * Microsoft Fluent Design System 交互逻辑
 */

// 应用程序主类
class AirMonitorApp {
    constructor() {
        this.currentPage = 'connection';
        this.isConnected = false;
        this.connectionStatus = 'disconnected';
        this.deviceInfo = null;
        this.sidebarCollapsed = false;
        
        this.init();
    }
    
    // 初始化应用程序
    init() {
        this.initEventListeners();
        this.initStatusUpdates();
        this.loadInitialPage();
        
        console.log('AirMonitor Pro 已启动');
    }
    
    // 初始化事件监听器
    initEventListeners() {
        // 导航菜单点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.currentTarget.closest('.nav-item').dataset.page;
                this.navigateToPage(page);
            });
        });
        
        // 侧边栏折叠按钮
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        // 头部按钮事件
        const settingsBtn = document.getElementById('settingsBtn');
        const helpBtn = document.getElementById('helpBtn');
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettings();
            });
        }
        
        if (helpBtn) {
            helpBtn.addEventListener('click', () => {
                this.showHelp();
            });
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    // 初始化状态更新
    initStatusUpdates() {
        // 更新时间戳
        this.updateTimestamp();
        setInterval(() => {
            this.updateTimestamp();
        }, 1000);
        
        // 模拟连接状态检查
        this.checkConnectionStatus();
        setInterval(() => {
            this.checkConnectionStatus();
        }, 5000);
    }
    
    // 加载初始页面
    loadInitialPage() {
        this.navigateToPage('connection');
        this.updateOperationStatus('应用程序已启动');
    }
    
    // 页面导航
    navigateToPage(page) {
        if (this.currentPage === page) return;
        
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const targetNavItem = document.querySelector(`[data-page="${page}"]`);
        if (targetNavItem) {
            targetNavItem.classList.add('active');
        }
        
        // 加载页面内容
        this.loadPageContent(page);
        this.currentPage = page;
        
        // 更新操作状态
        const pageNames = {
            'connection': '连接管理',
            'monitor': '实时监控',
            'control': '设备控制',
            'analysis': '数据分析',
            'debug': '调试工具',
            'system': '系统管理'
        };
        
        this.updateOperationStatus(`切换到${pageNames[page]}页面`);
    }
    
    // 加载页面内容
    loadPageContent(page) {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;
        
        // 显示加载状态
        mainContent.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载${this.getPageTitle(page)}...</div>
            </div>
        `;
        
        // 模拟异步加载
        setTimeout(() => {
            mainContent.innerHTML = this.getPageContent(page);
            mainContent.classList.add('fade-in');
        }, 500);
    }
    
    // 获取页面标题
    getPageTitle(page) {
        const titles = {
            'connection': '连接管理',
            'monitor': '实时监控',
            'control': '设备控制',
            'analysis': '数据分析',
            'debug': '调试工具',
            'system': '系统管理'
        };
        return titles[page] || '未知页面';
    }
    
    // 获取页面内容
    getPageContent(page) {
        const contents = {
            'connection': this.getConnectionPageContent(),
            'monitor': this.getMonitorPageContent(),
            'control': this.getControlPageContent(),
            'analysis': this.getAnalysisPageContent(),
            'debug': this.getDebugPageContent(),
            'system': this.getSystemPageContent()
        };
        
        return contents[page] || this.getDefaultPageContent(page);
    }
    
    // 连接管理页面内容
    getConnectionPageContent() {
        return `
            <div class="content-container">
                <div class="page-header">
                    <div class="page-title">
                        <svg class="page-icon" viewBox="0 0 20 20">
                            <path fill="currentColor" d="M10 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16zm0 14a6 6 0 1 1 0-12 6 6 0 0 1 0 12z"/>
                        </svg>
                        <div>
                            <h1>连接管理</h1>
                            <div class="page-subtitle">串口连接和设备通信管理</div>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="app.connectDevice()">连接设备</button>
                        <button class="btn btn-secondary">刷新端口</button>
                    </div>
                </div>

                <div class="content-grid cols-3">
                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">串口配置</h3>
                            <div class="card-actions">
                                <button class="btn btn-secondary" style="height: 24px; padding: 2px 8px; font-size: 11px;">配置</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; gap: 8px; font-size: 12px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span>端口:</span><strong>COM3</strong>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>波特率:</span><strong>9600</strong>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>数据位:</span><strong>8</strong>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>停止位:</span><strong>1</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">设备状态</h3>
                            <div class="card-actions">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #D13438;"></div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; gap: 8px; font-size: 12px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span>设备名称:</span><strong>未连接</strong>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>设备类型:</span><strong>--</strong>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>固件版本:</span><strong>--</strong>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>通信质量:</span><strong>--</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">连接日志</h3>
                            <div class="card-actions">
                                <button class="btn btn-secondary" style="height: 24px; padding: 2px 8px; font-size: 11px;">清空</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="font-family: monospace; font-size: 11px; color: #605E5C; line-height: 1.3;">
                                <div>12:34:56 - 正在扫描串口...</div>
                                <div>12:34:57 - 发现端口: COM1, COM3</div>
                                <div>12:34:58 - 等待用户选择...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 实时监控页面内容
    getMonitorPageContent() {
        return `
            <div class="content-container">
                <div class="page-header">
                    <div class="page-title">
                        <svg class="page-icon" viewBox="0 0 20 20">
                            <path fill="currentColor" d="M2 4v12h16V4H2zm14 10H4V6h12v8z"/>
                        </svg>
                        <div>
                            <h1>实时监控</h1>
                            <div class="page-subtitle">设备运行参数实时监控</div>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary">开始监控</button>
                        <button class="btn btn-secondary">暂停</button>
                        <button class="btn btn-secondary">导出数据</button>
                    </div>
                </div>

                <div class="content-grid cols-4">
                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">环境温度</h3>
                            <div class="card-actions">
                                <span style="font-size: 11px; color: #107C10;">●</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #005FB8; margin-bottom: 4px;">23.5°C</div>
                                <div style="font-size: 11px; color: #605E5C;">正常范围</div>
                                <div style="font-size: 10px; color: #8A8886; margin-top: 4px;">更新: 12:34:56</div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">系统压力</h3>
                            <div class="card-actions">
                                <span style="font-size: 11px; color: #FF8C00;">●</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #FF8C00; margin-bottom: 4px;">2.1 MPa</div>
                                <div style="font-size: 11px; color: #605E5C;">偏高</div>
                                <div style="font-size: 10px; color: #8A8886; margin-top: 4px;">更新: 12:34:55</div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">运行功率</h3>
                            <div class="card-actions">
                                <span style="font-size: 11px; color: #107C10;">●</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #107C10; margin-bottom: 4px;">3.2 kW</div>
                                <div style="font-size: 11px; color: #605E5C;">正常</div>
                                <div style="font-size: 10px; color: #8A8886; margin-top: 4px;">更新: 12:34:56</div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="card-header">
                            <h3 class="card-title">运行状态</h3>
                            <div class="card-actions">
                                <span style="font-size: 11px; color: #107C10;">●</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: 600; color: #107C10; margin-bottom: 4px;">运行中</div>
                                <div style="font-size: 11px; color: #605E5C;">制冷模式</div>
                                <div style="font-size: 10px; color: #8A8886; margin-top: 4px;">运行时间: 2h 15m</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">实时数据曲线</h3>
                        <div class="card-actions">
                            <button class="btn btn-secondary" style="height: 24px; padding: 2px 8px; font-size: 11px;">设置</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div style="height: 200px; background: #F3F2F1; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #8A8886;">
                            <div style="text-align: center;">
                                <div style="font-size: 14px; margin-bottom: 8px;">📊</div>
                                <div style="font-size: 12px;">实时数据图表区域</div>
                                <div style="font-size: 11px; margin-top: 4px;">温度、压力、功率曲线</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 默认页面内容
    getDefaultPageContent(page) {
        return `
            <div class="content-container">
                <div class="page-header">
                    <div class="page-title">
                        <h1>${this.getPageTitle(page)}</h1>
                        <div class="page-subtitle">该功能正在开发中</div>
                    </div>
                </div>
                
                <div class="content-card">
                    <div class="card-body">
                        <div class="empty-state">
                            <svg class="empty-state-icon" viewBox="0 0 64 64">
                                <path fill="currentColor" d="M32 8a24 24 0 1 0 0 48 24 24 0 0 0 0-48zm0 4a20 20 0 1 1 0 40 20 20 0 0 1 0-40z"/>
                            </svg>
                            <div class="empty-state-title">${this.getPageTitle(page)}功能</div>
                            <div class="empty-state-description">
                                该功能模块正在开发中，敬请期待。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 切换侧边栏
    toggleSidebar() {
        const sidebar = document.querySelector('.app-sidebar');
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
            this.sidebarCollapsed = !this.sidebarCollapsed;
        }
    }
    
    // 更新时间戳
    updateTimestamp() {
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { hour12: false });
            timeElement.textContent = timeString;
        }
    }
    
    // 检查连接状态
    checkConnectionStatus() {
        // 这里应该是实际的设备连接检查逻辑
        // 目前使用模拟数据
        const statusElement = document.getElementById('connectionStatus');
        const deviceElement = document.getElementById('deviceInfo');
        
        if (statusElement && deviceElement) {
            // 模拟连接状态变化
            if (Math.random() > 0.7) {
                this.updateConnectionStatus('connected', '空调主机 #001');
            } else {
                this.updateConnectionStatus('disconnected', '等待连接设备...');
            }
        }
    }
    
    // 更新连接状态
    updateConnectionStatus(status, deviceName) {
        const statusElement = document.getElementById('connectionStatus');
        const deviceElement = document.getElementById('deviceInfo');
        
        if (statusElement) {
            const dot = statusElement.querySelector('.status-dot');
            const text = statusElement.querySelector('.status-text');
            
            dot.className = `status-dot ${status}`;
            
            const statusTexts = {
                'connected': '已连接',
                'disconnected': '未连接',
                'connecting': '连接中'
            };
            
            text.textContent = statusTexts[status] || '未知状态';
        }
        
        if (deviceElement) {
            const nameElement = deviceElement.querySelector('.device-name');
            nameElement.textContent = deviceName;
        }
        
        this.connectionStatus = status;
    }
    
    // 更新操作状态
    updateOperationStatus(operation) {
        const operationElement = document.getElementById('currentOperation');
        if (operationElement) {
            operationElement.textContent = operation;
            operationElement.classList.add('updating');
            
            setTimeout(() => {
                operationElement.classList.remove('updating');
            }, 200);
        }
    }
    
    // 键盘快捷键处理
    handleKeyboardShortcuts(e) {
        // Ctrl + 数字键快速切换页面
        if (e.ctrlKey && e.key >= '1' && e.key <= '6') {
            e.preventDefault();
            const pages = ['connection', 'monitor', 'control', 'analysis', 'debug', 'system'];
            const pageIndex = parseInt(e.key) - 1;
            if (pages[pageIndex]) {
                this.navigateToPage(pages[pageIndex]);
            }
        }
        
        // F11 切换侧边栏
        if (e.key === 'F11') {
            e.preventDefault();
            this.toggleSidebar();
        }
    }
    
    // 窗口大小变化处理
    handleResize() {
        // 在小屏幕下自动折叠侧边栏
        if (window.innerWidth < 1280 && !this.sidebarCollapsed) {
            this.toggleSidebar();
        }
    }
    
    // 显示设置
    showSettings() {
        this.updateOperationStatus('打开设置');
        // TODO: 实现设置对话框
        console.log('显示设置对话框');
    }
    
    // 显示帮助
    showHelp() {
        this.updateOperationStatus('打开帮助');
        // TODO: 实现帮助对话框
        console.log('显示帮助对话框');
    }
    
    // 连接设备
    connectDevice() {
        this.updateOperationStatus('正在连接设备...');
        this.updateConnectionStatus('connecting', '正在连接...');
        
        // 模拟连接过程
        setTimeout(() => {
            this.updateConnectionStatus('connected', '空调主机 #001');
            this.updateOperationStatus('设备连接成功');
        }, 2000);
    }
}

// 全局函数
window.loadPage = function(page) {
    if (window.app) {
        window.app.navigateToPage(page);
    }
};

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AirMonitorApp();
});
