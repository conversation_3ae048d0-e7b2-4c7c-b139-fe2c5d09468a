/* ===== 商用空调调试监控软件 - 主样式文件 ===== */
/* Microsoft Fluent Design System 设计规范 */

/* CSS 变量定义 - Fluent Design System 色彩系统 */
:root {
  /* 主色调 */
  --primary-color: #005FB8;
  --primary-hover: #0078D4;
  --primary-pressed: #004578;
  --primary-light: #E3F2FD;
  
  /* 辅助色 */
  --secondary-color: #0078D4;
  --accent-color: #005FB8;
  
  /* 状态色 */
  --success-color: #107C10;
  --warning-color: #FF8C00;
  --error-color: #D13438;
  --info-color: #0078D4;
  
  /* 中性色 */
  --neutral-primary: #323130;
  --neutral-secondary: #605E5C;
  --neutral-tertiary: #8A8886;
  --neutral-quaternary: #C8C6C4;
  --neutral-light: #EDEBE9;
  --neutral-lighter: #F3F2F1;
  --neutral-lightest: #FAF9F8;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAF9F8;
  --bg-tertiary: #F3F2F1;
  --bg-overlay: rgba(0, 0, 0, 0.4);
  
  /* 边框色 */
  --border-color: #EDEBE9;
  --border-hover: #C8C6C4;
  --border-focus: #005FB8;
  
  /* 阴影 */
  --shadow-depth-4: 0 1.6px 3.6px 0 rgba(0, 0, 0, 0.132), 0 0.3px 0.9px 0 rgba(0, 0, 0, 0.108);
  --shadow-depth-8: 0 3.2px 7.2px 0 rgba(0, 0, 0, 0.132), 0 0.6px 1.8px 0 rgba(0, 0, 0, 0.108);
  --shadow-depth-16: 0 6.4px 14.4px 0 rgba(0, 0, 0, 0.132), 0 1.2px 3.6px 0 rgba(0, 0, 0, 0.108);
  
  /* 字体 */
  --font-family: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 圆角 */
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 8px;
  
  /* 布局尺寸 */
  --header-height: 48px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 48px;
  --footer-height: 32px;
  
  /* 最小分辨率适配 */
  --min-width: 1280px;
  --min-height: 768px;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--neutral-primary);
  background-color: var(--bg-secondary);
  overflow: hidden;
}

/* 主应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-width: var(--min-width);
  min-height: var(--min-height);
  background-color: var(--bg-secondary);
}

/* 主体内容区域 */
.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: 400;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 主要按钮 */
.btn-primary, .action-btn.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover, .action-btn.primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-primary:active, .action-btn.primary:active {
  background-color: var(--primary-pressed);
  border-color: var(--primary-pressed);
}

/* 次要按钮 */
.btn-secondary, .action-btn.secondary {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary:hover, .action-btn.secondary:hover {
  background-color: var(--primary-light);
}

/* 欢迎屏幕样式 */
.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: var(--bg-primary);
}

.welcome-content {
  text-align: center;
  max-width: 600px;
  padding: var(--spacing-xl);
}

.welcome-content h1 {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--neutral-primary);
  margin-bottom: var(--spacing-sm);
}

.welcome-content p {
  font-size: var(--font-size-lg);
  color: var(--neutral-secondary);
  margin-bottom: var(--spacing-xl);
}

.quick-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  user-select: none;
  white-space: nowrap;
}

.action-btn svg {
  flex-shrink: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-quaternary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-tertiary);
}

/* 响应式设计 */
@media (max-width: 1440px) {
  :root {
    --sidebar-width: 200px;
  }
}

@media (max-width: 1280px) {
  .app-container {
    min-width: 1280px;
  }
  
  .welcome-content {
    padding: var(--spacing-lg);
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 工具类 */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--neutral-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border { border: 1px solid var(--border-color); }
.border-hover:hover { border-color: var(--border-hover); }
.border-focus:focus { border-color: var(--border-focus); }

.shadow-sm { box-shadow: var(--shadow-depth-4); }
.shadow-md { box-shadow: var(--shadow-depth-8); }
.shadow-lg { box-shadow: var(--shadow-depth-16); }
