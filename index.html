<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商用空调调试监控软件 - AirMonitor Pro</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components/header.css">
    <link rel="stylesheet" href="css/components/sidebar.css">
    <link rel="stylesheet" href="css/components/main-content.css">
    <link rel="stylesheet" href="css/components/footer.css">
    <!-- Fluent UI Icons -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css">
</head>
<body>
    <!-- 主应用容器 -->
    <div class="app-container">
        <!-- 顶部标题栏 -->
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <svg class="logo-icon" width="24" height="24" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    <span class="app-title">AirMonitor Pro</span>
                    <span class="app-subtitle">商用空调调试监控软件</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="connection-status">
                    <div class="status-indicator" id="connectionStatus">
                        <span class="status-dot disconnected"></span>
                        <span class="status-text">未连接</span>
                    </div>
                    <div class="device-info" id="deviceInfo">
                        <span class="device-name">等待连接设备...</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="license-info">
                    <div class="license-status">
                        <svg class="license-icon" width="16" height="16" viewBox="0 0 16 16">
                            <path fill="currentColor" d="M8 1a7 7 0 1 0 0 14A7 7 0 0 0 8 1zM7 11V6h2v5H7zm0-6V3h2v2H7z"/>
                        </svg>
                        <span class="license-text">已授权</span>
                    </div>
                    <div class="user-info">
                        <span class="user-name">调试工程师</span>
                    </div>
                </div>
                
                <div class="header-actions">
                    <button class="header-btn" id="settingsBtn" title="设置">
                        <svg width="16" height="16" viewBox="0 0 16 16">
                            <path fill="currentColor" d="M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                        </svg>
                    </button>
                    <button class="header-btn" id="helpBtn" title="帮助">
                        <svg width="16" height="16" viewBox="0 0 16 16">
                            <path fill="currentColor" d="M8 1a7 7 0 1 0 0 14A7 7 0 0 0 8 1zM7 11V6h2v5H7zm0-6V3h2v2H7z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主体内容区域 -->
        <div class="app-body">
            <!-- 左侧导航栏 -->
            <nav class="app-sidebar">
                <div class="sidebar-content">
                    <ul class="nav-menu">
                        <li class="nav-item active" data-page="connection">
                            <a href="#" class="nav-link">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20">
                                    <path fill="currentColor" d="M10 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16zm0 14a6 6 0 1 1 0-12 6 6 0 0 1 0 12z"/>
                                </svg>
                                <span class="nav-text">连接管理</span>
                            </a>
                        </li>
                        
                        <li class="nav-item" data-page="monitor">
                            <a href="#" class="nav-link">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20">
                                    <path fill="currentColor" d="M2 4v12h16V4H2zm14 10H4V6h12v8z"/>
                                </svg>
                                <span class="nav-text">实时监控</span>
                            </a>
                        </li>
                        
                        <li class="nav-item" data-page="control">
                            <a href="#" class="nav-link">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20">
                                    <path fill="currentColor" d="M10 2L3 9l7 7 7-7-7-7z"/>
                                </svg>
                                <span class="nav-text">设备控制</span>
                            </a>
                        </li>
                        
                        <li class="nav-item" data-page="analysis">
                            <a href="#" class="nav-link">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20">
                                    <path fill="currentColor" d="M3 17h14V3H3v14zm2-12h10v2H5V5zm0 4h10v2H5V9zm0 4h7v2H5v-2z"/>
                                </svg>
                                <span class="nav-text">数据分析</span>
                            </a>
                        </li>
                        
                        <li class="nav-item" data-page="debug">
                            <a href="#" class="nav-link">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20">
                                    <path fill="currentColor" d="M10 1L5 6v4l5 5 5-5V6l-5-5z"/>
                                </svg>
                                <span class="nav-text">调试工具</span>
                            </a>
                        </li>
                        
                        <li class="nav-item" data-page="system">
                            <a href="#" class="nav-link">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 20 20">
                                    <path fill="currentColor" d="M10 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16zm0 3a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm2 8H8v-1h1V9H8V8h2v3h1v1z"/>
                                </svg>
                                <span class="nav-text">系统管理</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- 侧边栏底部 -->
                <div class="sidebar-footer">
                    <button class="sidebar-toggle" id="sidebarToggle" title="收起/展开">
                        <svg width="16" height="16" viewBox="0 0 16 16">
                            <path fill="currentColor" d="M6 12l4-4-4-4v8z"/>
                        </svg>
                    </button>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="app-main">
                <div class="main-content" id="mainContent">
                    <!-- 动态内容将在这里加载 -->
                    <div class="welcome-screen">
                        <div class="welcome-content">
                            <h1>AirMonitor Pro</h1>
                            <p>商用空调调试监控软件 - 专业工具版</p>

                            <!-- 快速状态概览 -->
                            <div class="status-overview">
                                <div class="status-item">
                                    <div class="status-icon">
                                        <svg width="16" height="16" viewBox="0 0 16 16">
                                            <circle cx="8" cy="8" r="6" fill="none" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">连接状态</div>
                                        <div class="status-value">未连接</div>
                                    </div>
                                </div>

                                <div class="status-item">
                                    <div class="status-icon">
                                        <svg width="16" height="16" viewBox="0 0 16 16">
                                            <rect x="2" y="3" width="12" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">监控设备</div>
                                        <div class="status-value">0 台</div>
                                    </div>
                                </div>

                                <div class="status-item">
                                    <div class="status-icon">
                                        <svg width="16" height="16" viewBox="0 0 16 16">
                                            <path d="M8 1l2 6h5l-4 3 1.5 5L8 12l-4.5 3L5 10 1 7h5l2-6z" fill="currentColor"/>
                                        </svg>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">系统状态</div>
                                        <div class="status-value">就绪</div>
                                    </div>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <button class="action-btn primary" onclick="loadPage('connection')">
                                    <svg width="16" height="16" viewBox="0 0 16 16">
                                        <circle cx="8" cy="8" r="6" fill="none" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    连接设备
                                </button>
                                <button class="action-btn secondary" onclick="loadPage('monitor')">
                                    <svg width="16" height="16" viewBox="0 0 16 16">
                                        <rect x="2" y="3" width="12" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    监控面板
                                </button>
                                <button class="action-btn secondary" onclick="loadPage('debug')">
                                    <svg width="16" height="16" viewBox="0 0 16 16">
                                        <path d="M8 1l2 6h5l-4 3 1.5 5L8 12l-4.5 3L5 10 1 7h5l2-6z" fill="currentColor"/>
                                    </svg>
                                    调试工具
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 底部状态栏 -->
        <footer class="app-footer">
            <div class="footer-left">
                <div class="status-info">
                    <span class="status-label">状态:</span>
                    <span class="status-value" id="currentStatus">就绪</span>
                </div>
                <div class="operation-info">
                    <span class="operation-label">操作:</span>
                    <span class="operation-value" id="currentOperation">等待用户操作</span>
                </div>
            </div>
            
            <div class="footer-center">
                <div class="timestamp">
                    <span class="time-label">时间:</span>
                    <span class="time-value" id="currentTime">--:--:--</span>
                </div>
            </div>
            
            <div class="footer-right">
                <div class="version-info">
                    <span class="version-label">版本:</span>
                    <span class="version-value">v1.0.0</span>
                </div>
                <div class="build-info">
                    <span class="build-value">Build 20240618</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/components/navigation.js"></script>
    <script src="js/components/status.js"></script>
</body>
</html>
