/**
 * 状态管理组件 - 处理应用程序状态和状态栏更新
 */

class StatusManager {
    constructor() {
        this.currentStatus = 'ready';
        this.connectionState = 'disconnected';
        this.deviceInfo = null;
        this.operationQueue = [];
        this.statusHistory = [];
        this.maxHistoryLength = 50;
        
        this.init();
    }
    
    init() {
        this.initStatusElements();
        this.initStatusUpdates();
        this.initProgressTracking();
    }
    
    // 初始化状态元素
    initStatusElements() {
        this.elements = {
            currentStatus: document.getElementById('currentStatus'),
            currentOperation: document.getElementById('currentOperation'),
            currentTime: document.getElementById('currentTime'),
            connectionStatus: document.getElementById('connectionStatus'),
            deviceInfo: document.getElementById('deviceInfo')
        };
    }
    
    // 初始化状态更新
    initStatusUpdates() {
        // 定期更新时间
        this.updateTime();
        this.timeInterval = setInterval(() => {
            this.updateTime();
        }, 1000);
        
        // 定期检查连接状态
        this.checkConnectionInterval = setInterval(() => {
            this.checkConnectionHealth();
        }, 5000);
        
        // 处理操作队列
        this.processOperationQueue();
        this.operationInterval = setInterval(() => {
            this.processOperationQueue();
        }, 100);
    }
    
    // 初始化进度跟踪
    initProgressTracking() {
        this.progressBar = this.createProgressBar();
        this.currentProgress = 0;
        this.isProgressVisible = false;
    }
    
    // 创建进度条
    createProgressBar() {
        const footer = document.querySelector('.app-footer');
        if (!footer) return null;
        
        const progressContainer = document.createElement('div');
        progressContainer.className = 'footer-progress';
        progressContainer.style.display = 'none';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'footer-progress-bar';
        
        progressContainer.appendChild(progressBar);
        footer.appendChild(progressContainer);
        
        return { container: progressContainer, bar: progressBar };
    }
    
    // 更新时间显示
    updateTime() {
        if (this.elements.currentTime) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            this.elements.currentTime.textContent = timeString;
        }
    }
    
    // 更新应用状态
    updateStatus(status, message = '') {
        this.currentStatus = status;
        
        if (this.elements.currentStatus) {
            this.elements.currentStatus.textContent = this.getStatusText(status);
            this.elements.currentStatus.className = `status-value ${status}`;
        }
        
        // 添加到历史记录
        this.addToStatusHistory({
            timestamp: new Date(),
            status: status,
            message: message
        });
        
        // 更新页面状态指示
        this.updatePageStatusIndicator(status);
        
        // 触发状态变化事件
        this.dispatchStatusEvent('statusChanged', { status, message });
    }
    
    // 获取状态文本
    getStatusText(status) {
        const statusTexts = {
            'ready': '就绪',
            'busy': '忙碌',
            'connecting': '连接中',
            'connected': '已连接',
            'disconnected': '未连接',
            'error': '错误',
            'warning': '警告',
            'processing': '处理中'
        };
        
        return statusTexts[status] || status;
    }
    
    // 更新操作状态
    updateOperation(operation, priority = 'normal') {
        const operationData = {
            text: operation,
            timestamp: new Date(),
            priority: priority,
            id: Date.now() + Math.random()
        };
        
        // 根据优先级插入队列
        if (priority === 'high') {
            this.operationQueue.unshift(operationData);
        } else {
            this.operationQueue.push(operationData);
        }
        
        // 立即处理高优先级操作
        if (priority === 'high') {
            this.processOperationQueue();
        }
    }
    
    // 处理操作队列
    processOperationQueue() {
        if (this.operationQueue.length === 0) return;
        
        const operation = this.operationQueue.shift();
        
        if (this.elements.currentOperation) {
            this.elements.currentOperation.textContent = operation.text;
            this.elements.currentOperation.classList.add('updating');
            
            setTimeout(() => {
                if (this.elements.currentOperation) {
                    this.elements.currentOperation.classList.remove('updating');
                }
            }, 300);
        }
        
        // 触发操作更新事件
        this.dispatchStatusEvent('operationChanged', operation);
    }
    
    // 更新连接状态
    updateConnectionStatus(state, deviceInfo = null) {
        this.connectionState = state;
        this.deviceInfo = deviceInfo;
        
        // 更新连接状态指示器
        if (this.elements.connectionStatus) {
            const statusDot = this.elements.connectionStatus.querySelector('.status-dot');
            const statusText = this.elements.connectionStatus.querySelector('.status-text');
            
            if (statusDot) {
                statusDot.className = `status-dot ${state}`;
            }
            
            if (statusText) {
                statusText.textContent = this.getConnectionStatusText(state);
            }
        }
        
        // 更新设备信息
        if (this.elements.deviceInfo && deviceInfo) {
            const deviceName = this.elements.deviceInfo.querySelector('.device-name');
            if (deviceName) {
                deviceName.textContent = deviceInfo.name || '未知设备';
            }
        }
        
        // 更新页脚状态
        this.updateFooterConnectionState(state);
        
        // 触发连接状态变化事件
        this.dispatchStatusEvent('connectionChanged', { state, deviceInfo });
    }
    
    // 获取连接状态文本
    getConnectionStatusText(state) {
        const stateTexts = {
            'connected': '已连接',
            'disconnected': '未连接',
            'connecting': '连接中',
            'error': '连接错误',
            'timeout': '连接超时'
        };
        
        return stateTexts[state] || state;
    }
    
    // 更新页脚连接状态
    updateFooterConnectionState(state) {
        const footer = document.querySelector('.app-footer');
        if (footer) {
            // 移除所有状态类
            footer.classList.remove('connected', 'disconnected', 'connecting', 'error');
            // 添加当前状态类
            footer.classList.add(state);
        }
    }
    
    // 显示进度条
    showProgress(progress = 0, indeterminate = false) {
        if (!this.progressBar) return;
        
        this.progressBar.container.style.display = 'block';
        this.isProgressVisible = true;
        
        if (indeterminate) {
            this.progressBar.bar.classList.add('indeterminate');
            this.progressBar.bar.style.width = '30%';
        } else {
            this.progressBar.bar.classList.remove('indeterminate');
            this.progressBar.bar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
        
        this.currentProgress = progress;
    }
    
    // 更新进度
    updateProgress(progress) {
        if (!this.isProgressVisible) return;
        
        this.showProgress(progress, false);
    }
    
    // 隐藏进度条
    hideProgress() {
        if (!this.progressBar) return;
        
        this.progressBar.container.style.display = 'none';
        this.isProgressVisible = false;
        this.currentProgress = 0;
    }
    
    // 检查连接健康状态
    checkConnectionHealth() {
        if (this.connectionState === 'connected' && this.deviceInfo) {
            // 模拟连接健康检查
            const isHealthy = Math.random() > 0.1; // 90% 健康率
            
            if (!isHealthy) {
                this.updateConnectionStatus('error', this.deviceInfo);
                this.updateOperation('设备连接异常，正在重试...', 'high');
                
                // 尝试重新连接
                setTimeout(() => {
                    this.updateConnectionStatus('connected', this.deviceInfo);
                    this.updateOperation('设备连接已恢复');
                }, 3000);
            }
        }
    }
    
    // 更新页面状态指示器
    updatePageStatusIndicator(status) {
        const indicator = document.querySelector('.footer-status-dot');
        if (indicator) {
            indicator.className = `footer-status-dot ${status}`;
        }
    }
    
    // 添加到状态历史
    addToStatusHistory(statusData) {
        this.statusHistory.push(statusData);
        
        // 限制历史记录长度
        if (this.statusHistory.length > this.maxHistoryLength) {
            this.statusHistory.shift();
        }
    }
    
    // 获取状态历史
    getStatusHistory() {
        return [...this.statusHistory];
    }
    
    // 获取当前状态信息
    getCurrentStatus() {
        return {
            status: this.currentStatus,
            connectionState: this.connectionState,
            deviceInfo: this.deviceInfo,
            timestamp: new Date()
        };
    }
    
    // 派发状态事件
    dispatchStatusEvent(eventType, data) {
        const event = new CustomEvent(eventType, {
            detail: data,
            bubbles: true
        });
        
        document.dispatchEvent(event);
    }
    
    // 显示状态消息
    showStatusMessage(message, type = 'info', duration = 3000) {
        // 创建状态消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `status-message status-message-${type}`;
        messageElement.textContent = message;
        
        // 添加样式
        Object.assign(messageElement.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 16px',
            borderRadius: '4px',
            color: 'white',
            fontSize: '14px',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease-in-out',
            maxWidth: '300px',
            wordWrap: 'break-word'
        });
        
        // 设置背景色
        const colors = {
            'info': 'var(--info-color)',
            'success': 'var(--success-color)',
            'warning': 'var(--warning-color)',
            'error': 'var(--error-color)'
        };
        
        messageElement.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(messageElement);
        
        // 显示动画
        requestAnimationFrame(() => {
            messageElement.style.opacity = '1';
            messageElement.style.transform = 'translateX(0)';
        });
        
        // 自动隐藏
        setTimeout(() => {
            messageElement.style.opacity = '0';
            messageElement.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 300);
        }, duration);
    }
    
    // 清理资源
    destroy() {
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
        }
        
        if (this.checkConnectionInterval) {
            clearInterval(this.checkConnectionInterval);
        }
        
        if (this.operationInterval) {
            clearInterval(this.operationInterval);
        }
    }
}

// 导出状态管理器
window.StatusManager = StatusManager;

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.statusManager = new StatusManager();
});
