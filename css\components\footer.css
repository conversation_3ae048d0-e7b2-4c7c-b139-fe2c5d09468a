/* ===== 底部状态栏样式 ===== */

.app-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--footer-height);
  padding: 0 var(--spacing-lg);
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  font-size: var(--font-size-xs);
  color: var(--neutral-secondary);
  flex-shrink: 0;
  z-index: 100;
}

/* 左侧区域 - 状态信息 */
.footer-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.status-info,
.operation-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-label,
.operation-label {
  font-weight: 500;
  color: var(--neutral-tertiary);
}

.status-value {
  font-weight: 400;
  color: var(--neutral-primary);
  transition: color 0.2s ease-in-out;
}

.status-value.ready {
  color: var(--success-color);
}

.status-value.busy {
  color: var(--warning-color);
}

.status-value.error {
  color: var(--error-color);
}

.operation-value {
  font-weight: 400;
  color: var(--neutral-secondary);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 中间区域 - 时间戳 */
.footer-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  margin: 0 var(--spacing-lg);
}

.timestamp {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px var(--spacing-xs);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  height: 20px;
}

.time-label {
  font-weight: 500;
  color: var(--neutral-tertiary);
}

.time-value {
  font-weight: 600;
  color: var(--neutral-primary);
  min-width: 50px;
  text-align: center;
  font-size: var(--font-size-xs);
}

/* 右侧区域 - 版本信息 */
.footer-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
  justify-content: flex-end;
}

.version-info,
.build-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.version-label {
  font-weight: 500;
  color: var(--neutral-tertiary);
}

.version-value {
  font-weight: 500;
  color: var(--primary-color);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.build-value {
  font-weight: 400;
  color: var(--neutral-tertiary);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-size-xs);
}

/* 状态指示器 */
.footer-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-right: var(--spacing-xs);
}

.footer-status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--success-color);
  animation: pulse-subtle 2s infinite;
}

.footer-status-dot.warning {
  background-color: var(--warning-color);
}

.footer-status-dot.error {
  background-color: var(--error-color);
}

/* 进度条（用于显示操作进度） */
.footer-progress {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--border-color);
  overflow: hidden;
}

.footer-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  width: 0%;
  transition: width 0.3s ease-in-out;
}

.footer-progress-bar.indeterminate {
  width: 30%;
  animation: progress-indeterminate 2s infinite linear;
}

@keyframes progress-indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* 动画效果 */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 状态变化动画 */
.status-value,
.operation-value,
.time-value {
  transition: all 0.2s ease-in-out;
}

.status-value.updating,
.operation-value.updating {
  transform: scale(1.05);
}

/* 工具提示样式 */
.footer-tooltip {
  position: relative;
}

.footer-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--neutral-primary);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  z-index: 1000;
  margin-bottom: var(--spacing-xs);
}

.footer-tooltip:hover::after {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .footer-left {
    gap: var(--spacing-md);
  }
  
  .footer-center {
    margin: 0 var(--spacing-md);
  }
  
  .footer-right {
    gap: var(--spacing-sm);
  }
  
  .operation-value {
    max-width: 150px;
  }
}

@media (max-width: 1280px) {
  .app-footer {
    padding: 0 var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
  
  .footer-left {
    gap: var(--spacing-sm);
  }
  
  .footer-center {
    margin: 0 var(--spacing-sm);
  }
  
  .footer-right {
    gap: var(--spacing-xs);
  }
  
  .operation-info {
    display: none;
  }
  
  .build-info {
    display: none;
  }
  
  .operation-value {
    max-width: 100px;
  }
  
  .timestamp {
    padding: 2px var(--spacing-xs);
  }
  
  .time-value {
    min-width: 50px;
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 1024px) {
  .footer-left {
    flex: 0 1 auto;
  }
  
  .footer-right {
    flex: 0 1 auto;
  }
  
  .status-info {
    display: none;
  }
}

/* 特殊状态样式 */
.app-footer.connecting {
  background-color: rgba(255, 140, 0, 0.1);
  border-top-color: var(--warning-color);
}

.app-footer.connected {
  background-color: rgba(16, 124, 16, 0.1);
  border-top-color: var(--success-color);
}

.app-footer.error {
  background-color: rgba(209, 52, 56, 0.1);
  border-top-color: var(--error-color);
}
